@startuml Diagramma classi progetto

skinparam classAttributeIconSize 0
skinparam classFontStyle bold
skinparam packageStyle rectangle

package "model.utenti" {
    abstract class Utente {
        - idUtente: Long
        - nome: String
        - cognome: String
        - email: String
        - passwordHash: String
        - numeroTelefono: String
        - tipoRuolo: TipoRuolo
        - isAttivo: boolean
        
        + getAuthorities(): Collection<GrantedAuthority>
        + getPassword(): String
        + getUsername(): String
        + isEnabled(): boolean
        + modificaPassword(nuovaPassword: String): boolean
        + disattivaAccount(): void
        + riattivaAccount(): void
    }
    
    class Acquirente {
        + Acquirente()
        + Acquirente(nome: String, cognome: String, email: String, passwordHash: String, numeroTelefono: String, tipoRuolo: TipoRuolo)
    }
    
    abstract class Venditore {
        - datiAzienda: DatiAzienda
        - prodottiOfferti: List<Prodotto>
        - statoAccreditamento: StatoAccreditamento
        
        + aggiungiCertificazione(certificazione: Certificazione): void
        + aggiungiProdottoOfferto(prodotto: Prodotto): void
    }
    
    class Produttore {
        + Produttore()
        + Produttore(nome: String, cognome: String, email: String, passwordHash: String, numeroTelefono: String, datiAzienda: DatiAzienda, tipoRuolo: TipoRuolo)
    }
    
    class Trasformatore {
        + Trasformatore()
        + Trasformatore(nome: String, cognome: String, email: String, passwordHash: String, numeroTelefono: String, datiAzienda: DatiAzienda, tipoRuolo: TipoRuolo)
        + getProdottiTrasformati(): List<Prodotto>
        + getProdottiColtivati(): List<Prodotto>
        + contaProdottiTrasformati(): int
        + contaProdottiColtivati(): int
        + offreProdottiTrasformati(): boolean
        + offreProdottiColtivati(): boolean
        + isProduttoreMisto(): boolean
        + getDescrizioneCapacita(): String
    }
    
    class DistributoreDiTipicita {
        + DistributoreDiTipicita()
        + DistributoreDiTipicita(nome: String, cognome: String, email: String, passwordHash: String, numeroTelefono: String, datiAzienda: DatiAzienda, tipoRuolo: TipoRuolo)
    }
    
    class Curatore {
        + Curatore()
        + Curatore(nome: String, cognome: String, email: String, passwordHash: String, numeroTelefono: String, tipoRuolo: TipoRuolo)
    }
    
    class AnimatoreDellaFiliera {
        + AnimatoreDellaFiliera()
        + AnimatoreDellaFiliera(nome: String, cognome: String, email: String, passwordHash: String, numeroTelefono: String, tipoRuolo: TipoRuolo)
    }
    
    class GestorePiattaforma {
        + GestorePiattaforma()
        + GestorePiattaforma(nome: String, cognome: String, email: String, passwordHash: String, numeroTelefono: String, tipoRuolo: TipoRuolo)
    }
    
    class DatiAzienda {
        - id: Long
        - nomeAzienda: String
        - partitaIva: String
        - indirizzoAzienda: String
        - descrizioneAzienda: String
        - logoUrl: String
        - sitoWebUrl: String
        - statoVerifica: StatoVerificaValori
        - feedbackVerificaContenuto: String
        - certificazioniAzienda: List<Certificazione>
        
        + aggiungiCertificazione(certificazione: Certificazione): void
        + getId(): Long
        + getFeedbackVerifica(): String
        + setFeedbackVerifica(feedback: String): void
        + setStatoVerifica(stato: StatoVerificaValori): void
        + getStatoVerifica(): StatoVerificaValori
    }
    
    enum TipoRuolo {
        ACQUIRENTE
        PRODUTTORE
        TRASFORMATORE
        DISTRIBUTORE_DI_TIPICITA
        CURATORE
        ANIMATORE_DELLA_FILIERA
        GESTORE_PIATTAFORMA
    }
    
    enum StatoAccreditamento {
        PENDING
        APPROVED
        REJECTED
    }
}

package "model.catalogo" {
    class Prodotto {
        - idProdotto: Long
        - nome: String
        - descrizione: String
        - prezzo: double
        - quantitaDisponibile: int
        - statoVerifica: StatoVerificaValori
        - feedbackVerifica: String
        - venditore: Venditore
        - certificazioniProdotto: List<Certificazione>
        - tipoOrigine: TipoOrigineProdotto
        - idProcessoTrasformazioneOriginario: Long
        - idMetodoDiColtivazione: Long
        
        + isTrasformato(): boolean
        + isColtivato(): boolean
        + setProcessoProduzione(processo: ProcessoTrasformazione): void
        + getProcessoProduzione(): ProcessoTrasformazione
        + aggiungiCertificazione(certificazione: Certificazione): void
        + getId(): Long
        + getNome(): String
        + getDescrizione(): String
        + getPrezzo(): double
        + getVenditore(): Venditore
    }
    
    class Pacchetto {
        - idPacchetto: Long
        - nome: String
        - descrizione: String
        - quantitaDisponibile: int
        - prezzoPacchetto: double
        - elementiInclusi: List<Acquistabile>
        - distributore: DistributoreDiTipicita
        
        + aggiungiElemento(elemento: Acquistabile): void
        + rimuoviElemento(elemento: Acquistabile): void
        + getId(): Long
        + getNome(): String
        + getDescrizione(): String
        + getPrezzo(): double
        + getVenditore(): Venditore
    }
    
    class Certificazione {
        - idCertificazione: Long
        - nomeCertificazione: String
        - enteRilascio: String
        - dataRilascio: Date
        - dataScadenza: Date
        - idProdottoAssociato: Long
        - idAziendaAssociata: Long
        
        + stampaCertificazione(): void
        + getId(): Long
        + getNome(): String
    }
    
    enum TipoOrigineProdotto {
        COLTIVATO_ALLEVATO
        TRASFORMATO
        + isTrasformato(): boolean
        + isColtivato(): boolean
    }
}

package "model.ordine" {
    class Ordine {
        - idOrdine: Long
        - dataOrdine: Date
        - importoTotale: double
        - acquirente: Acquirente
        - righeOrdine: List<RigaOrdine>
        - statoCorrente: StatoCorrente
        - stato: IStatoOrdine
        
        + getStatoOrdine(): StatoCorrente
        + getStato(): IStatoOrdine
        + setStato(nuovoStato: IStatoOrdine): void
        + processa(): void
        + spedisci(): void
        + annulla(): void
        + consegna(): void
        + paga(): void
    }
    
    class RigaOrdine {
        - idRigaOrdine: Long
        - quantita: int
        - prezzoUnitario: double
        - importoRiga: double
        - prodotto: Prodotto
        - ordine: Ordine
        
        + calcolaImportoRiga(): double
    }
    
    enum StatoCorrente {
        ATTESA_PAGAMENTO
        PAGATO_PRONTO_PER_LAVORAZIONE
        IN_LAVORAZIONE
        SPEDITO
        CONSEGNATO
        ANNULLATO
    }
}

package "model.ordine.stateOrdine" {
    interface IStatoOrdine {
        + processaOrdine(ordine: Ordine): void
        + spedisciOrdine(ordine: Ordine): void
        + annullaOrdine(ordine: Ordine): void
        + consegnaOrdine(ordine: Ordine): void
        + getStatoCorrente(): StatoCorrente
        + cambiaStato(ordine: Ordine, nuovoStato: IStatoOrdine): void
    }
    
    class StatoOrdineNuovoInAttesaDiPagamento {
        + processaOrdine(ordine: Ordine): void
        + spedisciOrdine(ordine: Ordine): void
        + annullaOrdine(ordine: Ordine): void
        + consegnaOrdine(ordine: Ordine): void
        + getStatoCorrente(): StatoCorrente
    }
    
    class StatoOrdinePagatoProntoPerLavorazione {
        + processaOrdine(ordine: Ordine): void
        + spedisciOrdine(ordine: Ordine): void
        + annullaOrdine(ordine: Ordine): void
        + consegnaOrdine(ordine: Ordine): void
        + getStatoCorrente(): StatoCorrente
    }
    
    class StatoOrdineInLavorazione {
        + processaOrdine(ordine: Ordine): void
        + spedisciOrdine(ordine: Ordine): void
        + annullaOrdine(ordine: Ordine): void
        + consegnaOrdine(ordine: Ordine): void
        + getStatoCorrente(): StatoCorrente
    }
    
    class StatoOrdineSpedito {
        + processaOrdine(ordine: Ordine): void
        + spedisciOrdine(ordine: Ordine): void
        + annullaOrdine(ordine: Ordine): void
        + consegnaOrdine(ordine: Ordine): void
        + getStatoCorrente(): StatoCorrente
    }
    
    class StatoOrdineConsegnato {
        + processaOrdine(ordine: Ordine): void
        + spedisciOrdine(ordine: Ordine): void
        + annullaOrdine(ordine: Ordine): void
        + consegnaOrdine(ordine: Ordine): void
        + getStatoCorrente(): StatoCorrente
    }
    
    class StatoOrdineAnnullato {
        + processaOrdine(ordine: Ordine): void
        + spedisciOrdine(ordine: Ordine): void
        + annullaOrdine(ordine: Ordine): void
        + consegnaOrdine(ordine: Ordine): void
        + getStatoCorrente(): StatoCorrente
    }
}

package "model.carrello" {
    class Carrello {
        - idCarrello: Long
        - dataCreazione: Date
        - dataUltimaModifica: Date
        - acquirente: Acquirente
        - elementi: List<ElementoCarrello>
        
        + aggiungiElemento(elemento: ElementoCarrello): void
        + rimuoviElemento(elemento: ElementoCarrello): void
        + calcolaTotale(): double
        + svuotaCarrello(): void
    }
    
    class ElementoCarrello {
        - idElementoCarrello: Long
        - quantita: int
        - acquistabile: Acquistabile
        - carrello: Carrello
        
        + calcolaImporto(): double
    }
}

package "model.eventi" {
    class Evento {
        - idEvento: Long
        - titolo: String
        - descrizione: String
        - dataInizio: Date
        - dataFine: Date
        - luogo: String
        - maxPartecipanti: int
        - costo: double
        - animatore: AnimatoreDellaFiliera
        - statoEvento: StatoEventoValori
        
        + aggiungiPartecipante(partecipante: Utente): boolean
        + rimuoviPartecipante(partecipante: Utente): boolean
        + isPostiDisponibili(): boolean
    }
    
    class EventoRegistrazione {
        - idRegistrazione: Long
        - dataRegistrazione: Date
        - partecipante: Utente
        - evento: Evento
        - haPartecipato: boolean
        
        + confermaPartecipazione(): void
    }
    
    enum StatoEventoValori {
        BOZZA
        PUBBLICATO
        IN_CORSO
        TERMINATO
        ANNULLATO
    }
}

package "model.trasformazione" {
    class ProcessoTrasformazione {
        - idProcesso: Long
        - nome: String
        - descrizione: String
        - dataCreazione: Date
        - trasformatore: Trasformatore
        - fasi: List<FaseLavorazione>
        - fontiMateriePrime: List<FonteMateriaPrima>
        
        + aggiungiFase(fase: FaseLavorazione): void
        + aggiungiFonteMateriaPrima(fonte: FonteMateriaPrima): void
        + calcolaTracciabildita(): String
    }
    
    class FaseLavorazione {
        - idFase: Long
        - numeroOrdine: int
        - nome: String
        - descrizione: String
        - durataStimata: int
        - processo: ProcessoTrasformazione
        
        + getOrdineEsecuzione(): int
    }
    
    abstract class FonteMateriaPrima {
        - idFonte: Long
        - quantitaUtilizzata: double
        - processo: ProcessoTrasformazione
        
        + getDescrizione(): String
    }
    
    class FonteInterna {
        - prodottoInterno: Prodotto
        
        + getDescrizione(): String
    }
    
    class FonteEsterna {
        - nomeFornitore: String
        - descrizioneMateria: String
        
        + getDescrizione(): String
    }
}

package "model.coltivazione" {
    class MetodoDiColtivazione {
        - idMetodo: Long
        - nome: String
        - descrizione: String
        - tipoColtivazione: String
        - sostenibilita: String
        
        + getDescrizioneCompleta(): String
    }
}

package "model.common" {
    interface Acquistabile {
        + getId(): Long
        + getNome(): String
        + getDescrizione(): String
        + getPrezzo(): double
        + getVenditore(): Venditore
    }
    
    interface ElementoVerificabile {
        + getId(): Long
        + getFeedbackVerifica(): String
        + setFeedbackVerifica(feedback: String): void
        + setStatoVerifica(stato: StatoVerificaValori): void
        + getStatoVerifica(): StatoVerificaValori
    }
    
    enum StatoVerificaValori {
        IN_REVISIONE
        APPROVATO
        RESPINTO
    }
}

package "service.factory" {
    interface UtenteFactory {
        + creaAcquirente(nome: String, cognome: String, email: String, passwordHash: String, numeroTelefono: String): Acquirente
        + creaProduttore(nome: String, cognome: String, email: String, passwordHash: String, numeroTelefono: String, datiAzienda: DatiAzienda): Produttore
        + creaTrasformatore(nome: String, cognome: String, email: String, passwordHash: String, numeroTelefono: String, datiAzienda: DatiAzienda): Trasformatore
        + creaDistributoreDiTipicita(nome: String, cognome: String, email: String, passwordHash: String, numeroTelefono: String, datiAzienda: DatiAzienda): DistributoreDiTipicita
        + creaCuratore(nome: String, cognome: String, email: String, passwordHash: String, numeroTelefono: String): Curatore
        + creaAnimatoreDellaFiliera(nome: String, cognome: String, email: String, passwordHash: String, numeroTelefono: String): AnimatoreDellaFiliera
        + creaGestorePiattaforma(nome: String, cognome: String, email: String, passwordHash: String, numeroTelefono: String): GestorePiattaforma
        + creaUtente(tipoRuolo: TipoRuolo, nome: String, cognome: String, email: String, passwordHash: String, numeroTelefono: String, datiAzienda: DatiAzienda): Utente
    }
    
    abstract class AbstractUtenteFactory {
        + creaAcquirente(nome: String, cognome: String, email: String, passwordHash: String, numeroTelefono: String): Acquirente
        + creaProduttore(nome: String, cognome: String, email: String, passwordHash: String, numeroTelefono: String, datiAzienda: DatiAzienda): Produttore
        + creaTrasformatore(nome: String, cognome: String, email: String, passwordHash: String, numeroTelefono: String, datiAzienda: DatiAzienda): Trasformatore
        + creaDistributoreDiTipicita(nome: String, cognome: String, email: String, passwordHash: String, numeroTelefono: String, datiAzienda: DatiAzienda): DistributoreDiTipicita
        + creaCuratore(nome: String, cognome: String, email: String, passwordHash: String, numeroTelefono: String): Curatore
        + creaAnimatoreDellaFiliera(nome: String, cognome: String, email: String, passwordHash: String, numeroTelefono: String): AnimatoreDellaFiliera
        + creaGestorePiattaforma(nome: String, cognome: String, email: String, passwordHash: String, numeroTelefono: String): GestorePiattaforma
        + creaUtente(tipoRuolo: TipoRuolo, nome: String, cognome: String, email: String, passwordHash: String, numeroTelefono: String, datiAzienda: DatiAzienda): Utente
    }
    
    class SimpleUtenteFactory {
        + creaAcquirente(nome: String, cognome: String, email: String, passwordHash: String, numeroTelefono: String): Acquirente
        + creaProduttore(nome: String, cognome: String, email: String, passwordHash: String, numeroTelefono: String, datiAzienda: DatiAzienda): Produttore
        + creaTrasformatore(nome: String, cognome: String, email: String, passwordHash: String, numeroTelefono: String, datiAzienda: DatiAzienda): Trasformatore
        + creaDistributoreDiTipicita(nome: String, cognome: String, email: String, passwordHash: String, numeroTelefono: String, datiAzienda: DatiAzienda): DistributoreDiTipicita
        + creaCuratore(nome: String, cognome: String, email: String, passwordHash: String, numeroTelefono: String): Curatore
        + creaAnimatoreDellaFiliera(nome: String, cognome: String, email: String, passwordHash: String, numeroTelefono: String): AnimatoreDellaFiliera
        + creaGestorePiattaforma(nome: String, cognome: String, email: String, passwordHash: String, numeroTelefono: String): GestorePiattaforma
    }
}

package "service.pagamento" {
    interface IMetodoPagamentoStrategy {
        + elaboraPagamento(ordine: Ordine): boolean
    }
    
    class PagamentoCartaCreditoStrategy {
        + elaboraPagamento(ordine: Ordine): boolean
    }
    
    class PagamentoPayPalStrategy {
        + elaboraPagamento(ordine: Ordine): boolean
    }
    
    class PagamentoSimulatoStrategy {
        + elaboraPagamento(ordine: Ordine): boolean
    }
    
    class PagamentoException {
        + PagamentoException(message: String)
        + PagamentoException(message: String, cause: Throwable)
    }
}

package "service.observer" {
    interface IOrdineObservable {
        + aggiungiObserver(observer: IVenditoreObserver): void
        + rimuoviObserver(observer: IVenditoreObserver): void
        + notificaObservers(ordine: Ordine, venditoreSpecifico: Venditore): void
    }
    
    interface IVenditoreObserver {
        + notificaCambioOrdine(ordine: Ordine): void
        + getVenditore(): Venditore
    }
    
    interface IProdottoObservable {
        + aggiungiObserver(observer: ICuratoreObserver): void
        + rimuoviObserver(observer: ICuratoreObserver): void
        + notificaObservers(prodotto: Prodotto): void
    }
    
    interface ICuratoreObserver {
        + notificaNuovoProdotto(prodotto: Prodotto): void
        + getCuratore(): Curatore
    }
}

package "service.impl" {
    class OrdineService {
        - ordineRepository: IOrdineRepository
        - rigaOrdineRepository: IRigaOrdineRepository
        - prodottoRepository: IProdottoRepository
        - observers: List<IVenditoreObserver>
        
        + creaOrdine(carrello: Carrello, metodoPagamento: IMetodoPagamentoStrategy): Ordine
        + cambiaStatoOrdine(idOrdine: Long, nuovoStato: IStatoOrdine): void
        + aggiungiObserver(observer: IVenditoreObserver): void
        + rimuoviObserver(observer: IVenditoreObserver): void
        + notificaObservers(ordine: Ordine, venditoreSpecifico: Venditore): void
    }
    
    class VenditoreObserverService {
        - venditore: Venditore
        
        + notificaCambioOrdine(ordine: Ordine): void
        + getVenditore(): Venditore
    }
    
    class ProdottoService {
        - prodottoRepository: IProdottoRepository
        - observers: List<ICuratoreObserver>
        
        + creaProdotto(prodotto: Prodotto): Prodotto
        + aggiornaQuantita(idProdotto: Long, nuovaQuantita: int): void
        + aggiungiObserver(observer: ICuratoreObserver): void
        + rimuoviObserver(observer: ICuratoreObserver): void
        + notificaObservers(prodotto: Prodotto): void
    }
    
    class CuratoreObserverService {
        - curatore: Curatore
        
        + notificaNuovoProdotto(prodotto: Prodotto): void
        + getCuratore(): Curatore
    }
    
    class CarrelloService {
        - carrelloRepository: ICarrelloRepository
        - prodottoRepository: IProdottoRepository
        
        + aggiungiProdotto(idCarrello: Long, idProdotto: Long, quantita: int): void
        + rimuoviProdotto(idCarrello: Long, idProdotto: Long): void
        + calcolaTotale(idCarrello: Long): double
        + svuotaCarrello(idCarrello: Long): void
    }
}

package "security" {
    class SecurityConfiguration {
        + passwordEncoder(): PasswordEncoder
        + authenticationManager(config: AuthenticationConfiguration): AuthenticationManager
        + filterChain(http: HttpSecurity): SecurityFilterChain
    }
    
    class JwtAuthenticationFilter {
        - jwtService: JwtService
        - userDetailsService: UserDetailsService
        
        + doFilterInternal(request: HttpServletRequest, response: HttpServletResponse, filterChain: FilterChain): void
    }
    
    class ApplicationConfig {
        - repository: IUtenteBaseRepository
        
        + userDetailsService(): UserDetailsService
        + authenticationProvider(): AuthenticationProvider
    }
}

package "dto" {
    class AuthenticationResponse {
        - token: String
        - refreshToken: String
        - expiresIn: long
        
        + getToken(): String
        + getRefreshToken(): String
        + getExpiresIn(): long
    }
    
    class LoginRequest {
        - email: String
        - password: String
        
        + getEmail(): String
        + getPassword(): String
    }
    
    class RegisterRequest {
        - nome: String
        - cognome: String
        - email: String
        - password: String
        - numeroTelefono: String
        - tipoRuolo: TipoRuolo
        - datiAzienda: DatiAzienda
        
        + validate(): boolean
    }
    
    class CertificazioneDTO {
        - nomeCertificazione: String
        - enteRilascio: String
        - dataRilascio: Date
        - dataScadenza: Date
        
        + toEntity(): Certificazione
    }
    
    class CreatePacchettoRequestDTO {
        - nome: String
        - descrizione: String
        - quantitaDisponibile: int
        - prezzoPacchetto: double
        - elementiPacchetto: List<ElementoPacchettoRequestDTO>
        
        + validate(): boolean
    }
    
    class ProductSummaryDTO {
        - idProdotto: Long
        - nome: String
        - prezzo: double
        - quantitaDisponibile: int
        - statoVerifica: StatoVerificaValori
        - tipoOrigine: TipoOrigineProdotto
        - nomeVenditore: String
        - idVenditore: Long
    }
    
    class ProductDetailDTO {
        - idProdotto: Long
        - nome: String
        - descrizione: String
        - prezzo: double
        - quantitaDisponibile: int
        - statoVerifica: StatoVerificaValori
        - feedbackVerifica: String
        - tipoOrigine: TipoOrigineProdotto
        - venditore: UserPublicDTO
        - certificazioni: List<CertificazioneDTO>
    }
    
    class CreateProductRequestDTO {
        - nome: String
        - descrizione: String
        - prezzo: double
        - quantitaDisponibile: int
        - tipoOrigine: TipoOrigineProdotto
        - certificazioni: List<CertificazioneDTO>
        
        + validate(): boolean
    }
    
    class CarrelloDTO {
        - idCarrello: Long
        - elementi: List<ElementoCarrelloDTO>
        - totale: double
        - dataUltimaModifica: Date
    }
    
    class ElementoCarrelloDTO {
        - idElementoCarrello: Long
        - acquistabile: ProductSummaryDTO
        - quantita: int
        - importo: double
    }
    
    class OrdineDetailDTO {
        - idOrdine: Long
        - dataOrdine: Date
        - importoTotale: double
        - statoCorrente: StatoCorrente
        - acquirente: UserPublicDTO
        - righeOrdine: List<RigaOrdineDTO>
    }
    
    class OrdineSummaryDTO {
        - idOrdine: Long
        - dataOrdine: Date
        - importoTotale: double
        - statoCorrente: StatoCorrente
        - numeroRighe: int
    }
    
    class UserPublicDTO {
        - idUtente: Long
        - nome: String
        - cognome: String
        - email: String
        - tipoRuolo: TipoRuolo
        - isAttivo: boolean
    }
    
    class UserDetailDTO {
        - idUtente: Long
        - nome: String
        - cognome: String
        - email: String
        - numeroTelefono: String
        - tipoRuolo: TipoRuolo
        - isAttivo: boolean
        - datiAzienda: DatiAzienda
    }
    
    class EventoDetailDTO {
        - idEvento: Long
        - titolo: String
        - descrizione: String
        - dataInizio: Date
        - dataFine: Date
        - luogo: String
        - maxPartecipanti: int
        - costo: double
        - animatore: UserPublicDTO
        - statoEvento: StatoEventoValori
        - partecipanti: List<UserPublicDTO>
    }
    
    class ProcessoTrasformazioneDTO {
        - idProcesso: Long
        - nome: String
        - descrizione: String
        - dataCreazione: Date
        - trasformatore: UserPublicDTO
        - fasi: List<FaseLavorazioneDTO>
        - fontiMateriePrime: List<FonteMateriaPrima>
    }
    
    class PacchettoSummaryDTO {
        - idPacchetto: Long
        - nome: String
        - prezzoPacchetto: double
        - quantitaDisponibile: int
        - nomeDistributore: String
        - idDistributore: Long
        - numeroElementi: int
    }
    
    class PacchettoDetailDTO {
        - idPacchetto: Long
        - nome: String
        - descrizione: String
        - quantitaDisponibile: int
        - prezzoPacchetto: double
        - distributore: UserPublicDTO
        - elementiInclusi: List<ElementoPacchettoDTO>
    }
    
    class ElementoPacchettoDTO {
        - acquistabile: ProductSummaryDTO
        - quantita: int
    }
    
    class ElementoPacchettoRequestDTO {
        - idAcquistabile: Long
        - quantita: int
        
        + validate(): boolean
    }
    
    class RigaOrdineDTO {
        - idRigaOrdine: Long
        - quantita: int
        - prezzoUnitario: double
        - importoRiga: double
        - prodotto: ProductSummaryDTO
    }
    
    class AddToCartRequestDTO {
        - idAcquistabile: Long
        - quantita: int
        
        + validate(): boolean
    }
    
    class CreateOrdineRequestDTO {
        - metodoPagamento: String
        - indirizzoSpedizione: String
        
        + validate(): boolean
    }
    
    class UpdateProductRequestDTO {
        - nome: String
        - descrizione: String
        - prezzo: double
        - quantitaDisponibile: int
        - certificazioni: List<CertificazioneDTO>
        
        + validate(): boolean
    }
    
    class UpdatePacchettoRequestDTO {
        - nome: String
        - descrizione: String
        - quantitaDisponibile: int
        - prezzoPacchetto: double
        - elementiPacchetto: List<ElementoPacchettoRequestDTO>
        
        + validate(): boolean
    }
    
    class EventoSummaryDTO {
        - idEvento: Long
        - titolo: String
        - dataInizio: Date
        - dataFine: Date
        - luogo: String
        - costo: double
        - statoEvento: StatoEventoValori
        - postiDisponibili: int
    }
    
    class CreateEventoRequestDTO {
        - titolo: String
        - descrizione: String
        - dataInizio: Date
        - dataFine: Date
        - luogo: String
        - maxPartecipanti: int
        - costo: double
        
        + validate(): boolean
    }
    
    class EventoRegistrazioneRequestDTO {
        - idEvento: Long
        
        + validate(): boolean
    }
    
    class EventoRegistrazioneDTO {
        - idRegistrazione: Long
        - dataRegistrazione: Date
        - partecipante: UserPublicDTO
        - evento: EventoSummaryDTO
        - haPartecipato: boolean
    }
    
    class FaseLavorazioneDTO {
        - idFase: Long
        - numeroOrdine: int
        - nome: String
        - descrizione: String
        - durataStimata: int
    }
    
    class CreateProcessoRequestDTO {
        - nome: String
        - descrizione: String
        - fasi: List<CreateFaseRequestDTO>
        - fontiMateriePrime: List<FonteMateriaPrima>
        
        + validate(): boolean
    }
    
    class CreateFaseRequestDTO {
        - numeroOrdine: int
        - nome: String
        - descrizione: String
        - durataStimata: int
        
        + validate(): boolean
    }
    
    class UpdateProcessoRequestDTO {
        - nome: String
        - descrizione: String
        - fasi: List<CreateFaseRequestDTO>
        - fontiMateriePrime: List<FonteMateriaPrima>
        
        + validate(): boolean
    }
    
    class TraceabilityDTO {
        - idProcesso: Long
        - nome: String
        - descrizione: String
        - fasi: List<FaseLavorazioneDTO>
        - fontiMateriePrime: List<FonteMateriaPrima>
        - tracciabilitaCompleta: String
    }
    
    class UserUpdateDTO {
        - nome: String
        - cognome: String
        - numeroTelefono: String
        - datiAzienda: DatiAzienda
        
        + validate(): boolean
    }
    
    class ModerationDecisionDTO {
        - decisione: StatoVerificaValori
        - feedback: String
        
        + validate(): boolean
    }
    
    class UserStatusUpdateDTO {
        - isAttivo: boolean
        - motivazione: String
        
        + validate(): boolean
    }
    
    class MetodoDiColtivazioneDTO {
        - idMetodo: Long
        - nome: String
        - descrizione: String
        - tipoColtivazione: String
        - sostenibilita: String
    }
    
    class ProductQuantityUpdateDTO {
        - quantitaDisponibile: int
        
        + validate(): boolean
    }
    
    class PackageCompositionDTO {
        - elementiInclusi: List<ElementoPacchettoDTO>
        - prezzoPacchetto: double
        - quantitaDisponibile: int
    }
    
    class EventoPartecipanteDTO {
        - partecipante: UserPublicDTO
        - dataRegistrazione: Date
        - haPartecipato: boolean
    }
}

package "exception" {
    class OrdineException {
        + OrdineException(message: String)
        + OrdineException(message: String, cause: Throwable)
    }
    
    class QuantitaNonDisponibileException {
        + QuantitaNonDisponibileException(message: String)
        + QuantitaNonDisponibileException(message: String, cause: Throwable)
    }
    
    class QuantitaNonDisponibileAlCheckoutException {
        + QuantitaNonDisponibileAlCheckoutException(message: String)
        + QuantitaNonDisponibileAlCheckoutException(message: String, cause: Throwable)
    }
    
    class CarrelloVuotoException {
        + CarrelloVuotoException(message: String)
        + CarrelloVuotoException(message: String, cause: Throwable)
    }
    
    class BusinessRuleViolationException {
        + BusinessRuleViolationException(message: String)
        + BusinessRuleViolationException(message: String, cause: Throwable)
    }
    
    class ModerationWorkflowException {
        + ModerationWorkflowException(message: String)
        + ModerationWorkflowException(message: String, cause: Throwable)
    }
    
    class ResourceOwnershipException {
        + ResourceOwnershipException(message: String)
        + ResourceOwnershipException(message: String, cause: Throwable)
    }
}

package "model.repository" {
    interface IUtenteBaseRepository {
        + findByEmail(email: String): Optional<Utente>
        + findAll(): List<Utente>
        + save(utente: Utente): Utente
        + deleteById(id: Long): void
    }
    
    interface IVenditoreRepository {
        + findByStatoAccreditamento(stato: StatoAccreditamento): List<Venditore>
        + findByDatiAzienda_PartitaIva(partitaIva: String): Optional<Venditore>
        + save(venditore: Venditore): Venditore
    }
    
    interface ICuratoreRepository {
        + findAll(): List<Curatore>
        + findById(id: Long): Optional<Curatore>
        + save(curatore: Curatore): Curatore
    }
    
    interface IAnimatoreRepository {
        + findAll(): List<AnimatoreDellaFiliera>
        + findById(id: Long): Optional<AnimatoreDellaFiliera>
        + save(animatore: AnimatoreDellaFiliera): AnimatoreDellaFiliera
    }
    
    interface IProdottoRepository {
        + findByVenditore(venditore: Venditore): List<Prodotto>
        + findByNomeContaining(nome: String): List<Prodotto>
        + findByStatoVerifica(stato: StatoVerificaValori): List<Prodotto>
        + existsByProcessoId(processoId: Long): boolean
        + findByTipoOrigine(tipo: TipoOrigineProdotto): List<Prodotto>
        + save(prodotto: Prodotto): Prodotto
    }
    
    interface IPacchettoRepository {
        + findByDistributore(distributore: DistributoreDiTipicita): List<Pacchetto>
        + findByNomeContaining(nome: String): List<Pacchetto>
        + save(pacchetto: Pacchetto): Pacchetto
    }
    
    interface IOrdineRepository {
        + findByAcquirente(acquirente: Acquirente): List<Ordine>
        + findByStatoCorrente(stato: StatoCorrente): List<Ordine>
        + findByDataOrdineBetween(dataInizio: Date, dataFine: Date): List<Ordine>
        + save(ordine: Ordine): Ordine
    }
    
    interface IRigaOrdineRepository {
        + findByOrdine(ordine: Ordine): List<RigaOrdine>
        + save(rigaOrdine: RigaOrdine): RigaOrdine
    }
    
    interface ICarrelloRepository {
        + findByAcquirente(acquirente: Acquirente): Optional<Carrello>
        + save(carrello: Carrello): Carrello
        + deleteById(id: Long): void
    }
    
    interface IEventoRepository {
        + findByAnimatore(animatore: AnimatoreDellaFiliera): List<Evento>
        + findByStatoEvento(stato: StatoEventoValori): List<Evento>
        + findByDataInizioBetween(dataInizio: Date, dataFine: Date): List<Evento>
        + save(evento: Evento): Evento
    }
    
    interface IEventoRegistrazioneRepository {
        + findByEvento(evento: Evento): List<EventoRegistrazione>
        + findByPartecipante(partecipante: Utente): List<EventoRegistrazione>
        + save(registrazione: EventoRegistrazione): EventoRegistrazione
    }
    
    interface IProcessoTrasformazioneRepository {
        + findByTrasformatore(trasformatore: Trasformatore): List<ProcessoTrasformazione>
        + findByNomeContaining(nome: String): List<ProcessoTrasformazione>
        + save(processo: ProcessoTrasformazione): ProcessoTrasformazione
    }
    
    interface ICertificazioneRepository {
        + findByIdProdottoAssociato(idProdotto: Long): List<Certificazione>
        + findByIdAziendaAssociata(idAzienda: Long): List<Certificazione>
        + findByDataScadenzaBefore(data: Date): List<Certificazione>
        + save(certificazione: Certificazione): Certificazione
    }
    
    interface IMetodoDiColtivazioneRepository {
        + findByTipoColtivazione(tipo: String): List<MetodoDiColtivazione>
        + findByNomeContaining(nome: String): List<MetodoDiColtivazione>
        + save(metodo: MetodoDiColtivazione): MetodoDiColtivazione
    }
    
    interface IDatiAziendaRepository {
        + findByPartitaIva(partitaIva: String): Optional<DatiAzienda>
        + findByStatoVerifica(stato: StatoVerificaValori): List<DatiAzienda>
        + save(datiAzienda: DatiAzienda): DatiAzienda
    }
}

package "controller" {
    class ProdottoController {
        - prodottoService: IProdottoService
        - prodottoMapper: ProdottoMapper
        - certificazioneService: ICertificazioneService
        - utenteService: IUtenteService
        - ownershipValidationService: OwnershipValidationService
        
        + getAllProducts(): ResponseEntity<Page<ProductSummaryDTO>>
        + getProductById(id: Long): ResponseEntity<ProductDetailDTO>
        + createProduct(request: CreateProductRequestDTO): ResponseEntity<ProductDetailDTO>
        + updateProduct(id: Long, request: UpdateProductRequestDTO): ResponseEntity<ProductDetailDTO>
        + deleteProduct(id: Long): ResponseEntity<Void>
    }
    
    class CarrelloController {
        - carrelloService: ICarrelloService
        - carrelloMapper: CarrelloMapper
        
        + getCarrello(): ResponseEntity<CarrelloDTO>
        + addToCart(request: AddToCartRequestDTO): ResponseEntity<CarrelloDTO>
        + removeFromCart(idElemento: Long): ResponseEntity<CarrelloDTO>
        + clearCart(): ResponseEntity<Void>
    }
    
    class OrdineController {
        - ordineService: IOrdineService
        - ordineMapper: OrdineMapper
        
        + createOrdine(request: CreateOrdineRequestDTO): ResponseEntity<OrdineDetailDTO>
        + getOrdiniUtente(): ResponseEntity<List<OrdineSummaryDTO>>
        + getOrdineById(id: Long): ResponseEntity<OrdineDetailDTO>
        + cambiaStatoOrdine(id: Long, nuovoStato: StatoCorrente): ResponseEntity<OrdineDetailDTO>
    }
    
    class EventoController {
        - eventoService: IEventoService
        - eventoMapper: EventoMapper
        
        + getAllEventi(): ResponseEntity<List<EventoSummaryDTO>>
        + getEventoById(id: Long): ResponseEntity<EventoDetailDTO>
        + createEvento(request: CreateEventoRequestDTO): ResponseEntity<EventoDetailDTO>
        + registrazioneEvento(id: Long): ResponseEntity<Void>
    }
    
    class PacchettoController {
        - pacchettoService: IPacchettoService
        - pacchettoMapper: PacchettoMapper
        
        + getAllPacchetti(): ResponseEntity<List<PacchettoSummaryDTO>>
        + getPacchettoById(id: Long): ResponseEntity<PacchettoDetailDTO>
        + createPacchetto(request: CreatePacchettoRequestDTO): ResponseEntity<PacchettoDetailDTO>
        + updatePacchetto(id: Long, request: UpdatePacchettoRequestDTO): ResponseEntity<PacchettoDetailDTO>
    }
    
    class ProcessoTrasformazioneController {
        - processoService: IProcessoTrasformazioneService
        - processoMapper: ProcessoMapper
        
        + getAllProcessi(): ResponseEntity<List<ProcessoTrasformazioneDTO>>
        + getProcessoById(id: Long): ResponseEntity<ProcessoTrasformazioneDTO>
        + createProcesso(request: CreateProcessoRequestDTO): ResponseEntity<ProcessoTrasformazioneDTO>
        + getTraceability(id: Long): ResponseEntity<TraceabilityDTO>
    }
    
    class AdminController {
        - gestoreService: IGestoreService
        - utenteService: IUtenteService
        
        + getModerationQueue(): ResponseEntity<List<ElementoVerificabile>>
        + approveItem(id: Long, decision: ModerationDecisionDTO): ResponseEntity<Void>
        + rejectItem(id: Long, decision: ModerationDecisionDTO): ResponseEntity<Void>
        + updateUserStatus(id: Long, request: UserStatusUpdateDTO): ResponseEntity<Void>
    }
    
    class GestorePiattaformaController {
        - gestoreService: IGestoreService
        - metricsService: MetricsService
        
        + getDashboardStats(): ResponseEntity<DashboardStatsDTO>
        + exportData(): ResponseEntity<byte[]>
        + getSystemHealth(): ResponseEntity<Map<String, Object>>
    }
    
    class AziendaController {
        - venditoreService: IVenditoreService
        - aziendaMapper: AziendaMapper
        
        + updateDatiAzienda(request: UpdateAziendaRequestDTO): ResponseEntity<DatiAzienda>
        + uploadLogo(file: MultipartFile): ResponseEntity<String>
        + addCertificazione(request: CertificazioneDTO): ResponseEntity<Void>
    }
    
    class OrdineVenditoreController {
        - ordineService: IOrdineService
        - venditoreObserverService: VenditoreObserverService
        
        + getOrdiniVenditore(): ResponseEntity<List<OrdineSummaryDTO>>
        + processaOrdine(id: Long): ResponseEntity<OrdineDetailDTO>
        + spedisciOrdine(id: Long): ResponseEntity<OrdineDetailDTO>
    }
    
    class GlobalExceptionHandler {
        + handleBusinessRuleViolation(ex: BusinessRuleViolationException): ResponseEntity<ErrorResponse>
        + handleQuantitaNonDisponibile(ex: QuantitaNonDisponibileException): ResponseEntity<ErrorResponse>
        + handleCarrelloVuoto(ex: CarrelloVuotoException): ResponseEntity<ErrorResponse>
        + handleValidationErrors(ex: MethodArgumentNotValidException): ResponseEntity<ErrorResponse>
        + handleGenericException(ex: Exception): ResponseEntity<ErrorResponse>
    }
    
    class ErrorResponse {
        - timestamp: LocalDateTime
        - status: int
        - error: String
        - message: String
        - path: String
        
        + ErrorResponse(status: int, error: String, message: String, path: String)
    }
}

package "validation" {
    annotation ValidDateRange {
        + start(): String
        + end(): String
        + message(): String
        + groups(): Class<?>[]
        + payload(): Class<? extends Payload>[]
    }
    
    class DateRangeValidator {
        + isValid(object: Object, context: ConstraintValidatorContext): boolean
    }
    
    annotation ValidCapacity {
        + message(): String
        + groups(): Class<?>[]
        + payload(): Class<? extends Payload>[]
    }
    
    class CapacityValidator {
        + isValid(capacity: Integer, context: ConstraintValidatorContext): boolean
    }
}

package "service.mapper" {
    interface ProdottoMapper {
        + toSummaryDTO(prodotto: Prodotto): ProductSummaryDTO
        + toDetailDTO(prodotto: Prodotto): ProductDetailDTO
        + toEntity(dto: CreateProductRequestDTO): Prodotto
        + updateEntity(dto: UpdateProductRequestDTO, prodotto: Prodotto): void
    }
    
    interface CarrelloMapper {
        + toDTO(carrello: Carrello): CarrelloDTO
        + toElementoDTO(elemento: ElementoCarrello): ElementoCarrelloDTO
    }
    
    interface OrdineMapper {
        + toDetailDTO(ordine: Ordine): OrdineDetailDTO
        + toSummaryDTO(ordine: Ordine): OrdineSummaryDTO
        + toRigaDTO(riga: RigaOrdine): RigaOrdineDTO
    }
    
    interface UtenteMapper {
        + toPublicDTO(utente: Utente): UserPublicDTO
        + toDetailDTO(utente: Utente): UserDetailDTO
        + updateEntity(dto: UserUpdateDTO, utente: Utente): void
    }
    
    interface EventoMapper {
        + toDetailDTO(evento: Evento): EventoDetailDTO
        + toSummaryDTO(evento: Evento): EventoSummaryDTO
        + toEntity(dto: CreateEventoRequestDTO): Evento
    }
    
    interface PacchettoMapper {
        + toDetailDTO(pacchetto: Pacchetto): PacchettoDetailDTO
        + toSummaryDTO(pacchetto: Pacchetto): PacchettoSummaryDTO
        + toEntity(dto: CreatePacchettoRequestDTO): Pacchetto
    }
    
    interface ProcessoMapper {
        + toDTO(processo: ProcessoTrasformazione): ProcessoTrasformazioneDTO
        + toEntity(dto: CreateProcessoRequestDTO): ProcessoTrasformazione
        + toTraceabilityDTO(processo: ProcessoTrasformazione): TraceabilityDTO
    }
    
    interface CertificazioneMapper {
        + toDTO(certificazione: Certificazione): CertificazioneDTO
        + toEntity(dto: CertificazioneDTO): Certificazione
    }
    
    interface MetodoDiColtivazioneMapper {
        + toDTO(metodo: MetodoDiColtivazione): MetodoDiColtivazioneDTO
        + toEntity(dto: MetodoDiColtivazioneDTO): MetodoDiColtivazione
    }
}

package "service.additional" {
    class MetricsService {
        - productViewCounter: Counter
        - orderCreatedCounter: Counter
        - userRegistrationCounter: Counter
        - authenticationAttemptCounter: Counter
        - authenticationSuccessCounter: Counter
        - cacheHitCounter: Counter
        - cacheMissCounter: Counter
        - productSearchTimer: Timer
        - orderProcessingTimer: Timer
        - databaseQueryTimer: Timer
        
        + incrementProductViews(): void
        + incrementOrdersCreated(): void
        + incrementUserRegistrations(): void
        + incrementAuthenticationAttempts(): void
        + incrementAuthenticationSuccess(): void
        + incrementCacheHits(): void
        + incrementCacheMisses(): void
        + recordProductSearchTime(duration: Duration): void
        + recordOrderProcessingTime(duration: Duration): void
        + recordDatabaseQueryTime(duration: Duration): void
    }
    
    class OwnershipValidationService {
        - utenteService: IUtenteService
        
        + validateProductOwnership(prodottoId: Long, utenteId: Long): boolean
        + validatePacchettoOwnership(pacchettoId: Long, utenteId: Long): boolean
        + validateOrdineAccess(ordineId: Long, utenteId: Long): boolean
        + validateEventoOwnership(eventoId: Long, utenteId: Long): boolean
        + validateProcessoOwnership(processoId: Long, utenteId: Long): boolean
    }
}

package "application" {
    class PiattaformaAgricolaLocaleApplication {
        + main(args: String[]): void
    }
}

' User Hierarchy - Inheritance
Utente <|-- Acquirente
Utente <|-- Venditore
Utente <|-- Curatore
Utente <|-- AnimatoreDellaFiliera
Utente <|-- GestorePiattaforma
Venditore <|-- Produttore
Venditore <|-- Trasformatore
Venditore <|-- DistributoreDiTipicita

' Composition and Aggregation
Venditore *-- DatiAzienda
Venditore "1" --> "0..*" Prodotto : offers
DatiAzienda "1" --> "0..*" Certificazione : has
Utente --> TipoRuolo : uses
Venditore --> StatoAccreditamento : has

' Catalog Relationships
Prodotto --> Venditore : sold by
Pacchetto --> DistributoreDiTipicita : created by
Pacchetto "1" --> "0..*" Acquistabile : contains
Prodotto "1" --> "0..*" Certificazione : has
Prodotto --> TipoOrigineProdotto : has
Prodotto --> ProcessoTrasformazione : produced by

' Interface Implementations
Prodotto ..|> Acquistabile : implements
Pacchetto ..|> Acquistabile : implements
Prodotto ..|> ElementoVerificabile : implements
DatiAzienda ..|> ElementoVerificabile : implements
ElementoVerificabile --> StatoVerificaValori : uses

' Order Management
Ordine --> Acquirente : placed by
Ordine "1" --> "1..*" RigaOrdine : contains
RigaOrdine --> Prodotto : references
Ordine --> StatoCorrente : has
Ordine --> IStatoOrdine : uses

' State Pattern Implementation
IStatoOrdine <|.. StatoOrdineNuovoInAttesaDiPagamento : implements
IStatoOrdine <|.. StatoOrdinePagatoProntoPerLavorazione : implements
IStatoOrdine <|.. StatoOrdineInLavorazione : implements
IStatoOrdine <|.. StatoOrdineSpedito : implements
IStatoOrdine <|.. StatoOrdineConsegnato : implements
IStatoOrdine <|.. StatoOrdineAnnullato : implements

' Cart Management
Carrello --> Acquirente : belongs to
Carrello "1" --> "0..*" ElementoCarrello : contains
ElementoCarrello --> Acquistabile : references

' Events
Evento --> AnimatoreDellaFiliera : organized by
EventoRegistrazione --> Utente : participant
EventoRegistrazione --> Evento : for event
Evento --> StatoEventoValori : has

' Transformation Process
ProcessoTrasformazione --> Trasformatore : managed by
ProcessoTrasformazione "1" --> "0..*" FaseLavorazione : contains
ProcessoTrasformazione "1" --> "0..*" FonteMateriaPrima : uses
FonteMateriaPrima <|-- FonteInterna
FonteMateriaPrima <|-- FonteEsterna
FonteInterna --> Prodotto : uses

' Cultivation
Prodotto --> MetodoDiColtivazione : uses

' Factory Pattern
UtenteFactory <|.. AbstractUtenteFactory : implements
AbstractUtenteFactory <|-- SimpleUtenteFactory

' Strategy Pattern
IMetodoPagamentoStrategy <|.. PagamentoCartaCreditoStrategy : implements
IMetodoPagamentoStrategy <|.. PagamentoPayPalStrategy : implements
IMetodoPagamentoStrategy <|.. PagamentoSimulatoStrategy : implements

' Observer Pattern
IOrdineObservable <|.. OrdineService : implements
IVenditoreObserver <|.. VenditoreObserverService : implements
IProdottoObservable <|.. ProdottoService : implements
ICuratoreObserver <|.. CuratoreObserverService : implements

' Service Dependencies
OrdineService --> IMetodoPagamentoStrategy : uses
VenditoreObserverService --> Venditore : observes
CuratoreObserverService --> Curatore : notifies

' Controller Dependencies
ProdottoController --> IProdottoService : uses
ProdottoController --> ProdottoMapper : uses
ProdottoController --> ICertificazioneService : uses
ProdottoController --> IUtenteService : uses
ProdottoController --> OwnershipValidationService : uses

CarrelloController --> ICarrelloService : uses
CarrelloController --> CarrelloMapper : uses

OrdineController --> IOrdineService : uses
OrdineController --> OrdineMapper : uses

EventoController --> IEventoService : uses
EventoController --> EventoMapper : uses

PacchettoController --> IPacchettoService : uses
PacchettoController --> PacchettoMapper : uses

ProcessoTrasformazioneController --> IProcessoTrasformazioneService : uses
ProcessoTrasformazioneController --> ProcessoMapper : uses

AdminController --> IGestoreService : uses
AdminController --> IUtenteService : uses

GestorePiattaformaController --> IGestoreService : uses
GestorePiattaformaController --> MetricsService : uses

AziendaController --> IVenditoreService : uses

OrdineVenditoreController --> IOrdineService : uses
OrdineVenditoreController --> VenditoreObserverService : uses

' Validation Relationships
ValidDateRange --> DateRangeValidator : validated by
ValidCapacity --> CapacityValidator : validated by

' Mapper Dependencies
ProdottoMapper --> Prodotto : maps
ProdottoMapper --> ProductSummaryDTO : creates
ProdottoMapper --> ProductDetailDTO : creates
ProdottoMapper --> CreateProductRequestDTO : uses

CarrelloMapper --> Carrello : maps
CarrelloMapper --> CarrelloDTO : creates
CarrelloMapper --> ElementoCarrelloDTO : creates

OrdineMapper --> Ordine : maps
OrdineMapper --> OrdineDetailDTO : creates
OrdineMapper --> OrdineSummaryDTO : creates

UtenteMapper --> Utente : maps
UtenteMapper --> UserPublicDTO : creates
UtenteMapper --> UserDetailDTO : creates

EventoMapper --> Evento : maps
EventoMapper --> EventoDetailDTO : creates

PacchettoMapper --> Pacchetto : maps
PacchettoMapper --> PacchettoDetailDTO : creates

ProcessoMapper --> ProcessoTrasformazione : maps
ProcessoMapper --> ProcessoTrasformazioneDTO : creates

CertificazioneMapper --> Certificazione : maps
CertificazioneMapper --> CertificazioneDTO : creates

MetodoDiColtivazioneMapper --> MetodoDiColtivazione : maps
MetodoDiColtivazioneMapper --> MetodoDiColtivazioneDTO : creates

' Additional DTO Relationships
PacchettoDetailDTO --> UserPublicDTO : contains
PacchettoDetailDTO --> ElementoPacchettoDTO : contains
ElementoPacchettoDTO --> ProductSummaryDTO : references
RigaOrdineDTO --> ProductSummaryDTO : references
EventoRegistrazioneDTO --> UserPublicDTO : contains
EventoRegistrazioneDTO --> EventoSummaryDTO : references
ProcessoTrasformazioneDTO --> FaseLavorazioneDTO : contains
TraceabilityDTO --> FaseLavorazioneDTO : contains
EventoPartecipanteDTO --> UserPublicDTO : references

' DTO to Entity Mappings
CreateProductRequestDTO --> Prodotto : maps to
UpdateProductRequestDTO --> Prodotto : updates
CreatePacchettoRequestDTO --> Pacchetto : maps to
UpdatePacchettoRequestDTO --> Pacchetto : updates
CreateEventoRequestDTO --> Evento : maps to
CreateProcessoRequestDTO --> ProcessoTrasformazione : maps to
UpdateProcessoRequestDTO --> ProcessoTrasformazione : updates
CreateOrdineRequestDTO --> Ordine : maps to
AddToCartRequestDTO --> ElementoCarrello : maps to
RegisterRequest --> Utente : maps to
LoginRequest --> AuthenticationResponse : produces

' Repository Dependencies in Service Layer
OrdineService --> IRigaOrdineRepository : uses
ProdottoService --> ICertificazioneRepository : uses
EventoService --> IEventoRepository : uses
EventoService --> IEventoRegistrazioneRepository : uses
ProcessoTrasformazioneService --> IProcessoTrasformazioneRepository : uses
CertificazioneService --> ICertificazioneRepository : uses
UtenteService --> IVenditoreRepository : uses
UtenteService --> ICuratoreRepository : uses
UtenteService --> IAnimatoreRepository : uses
VenditoreService --> IDatiAziendaRepository : uses
DistributoreService --> IPacchettoRepository : uses
CuratoreService --> ICuratoreRepository : uses
AnimatoreService --> IAnimatoreRepository : uses
MetodoDiColtivazioneService --> IMetodoDiColtivazioneRepository : uses

' Repository to Entity Relationships
IVenditoreRepository --> Venditore : manages
ICuratoreRepository --> Curatore : manages
IAnimatoreRepository --> AnimatoreDellaFiliera : manages
IPacchettoRepository --> Pacchetto : manages
IRigaOrdineRepository --> RigaOrdine : manages
IEventoRepository --> Evento : manages
IEventoRegistrazioneRepository --> EventoRegistrazione : manages
IProcessoTrasformazioneRepository --> ProcessoTrasformazione : manages
ICertificazioneRepository --> Certificazione : manages
IMetodoDiColtivazioneRepository --> MetodoDiColtivazione : manages
IDatiAziendaRepository --> DatiAzienda : manages

' Service Implementation Notes
note right of OrdineService : "Implements Observer Pattern\nfor vendor notifications"

note right of ProdottoService : "Implements Observer Pattern\nfor curator notifications"

note right of VenditoreObserverService : "Observer implementation\nfor vendor order updates"

note right of CuratoreObserverService : "Observer implementation\nfor curator product notifications"

note right of SimpleUtenteFactory : "Factory Pattern implementation\nfor creating different user types"

note right of PagamentoCartaCreditoStrategy : "Strategy Pattern implementation\nfor different payment methods"

note top of IStatoOrdine : "State Pattern interface\nfor order state management"

note right of ValidDateRange : "Custom validation annotation\nfor date range validation"

note right of MetricsService : "Provides business metrics\nfor monitoring and analytics"

note right of OwnershipValidationService : "Validates resource ownership\nfor security purposes"
@enduml